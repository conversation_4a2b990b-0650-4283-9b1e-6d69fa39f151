<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Awesome Icon Test</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" 
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" 
          crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a2e;
            color: white;
        }
        .icon-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            background: #16213e;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #7E57C2;
        }
        .icon-item i {
            font-size: 2rem;
            color: #7E57C2;
            margin-bottom: 10px;
            display: block;
        }
        .icon-name {
            font-size: 0.9rem;
            color: #ccc;
        }
        .test-result {
            background: #2d3748;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Font Awesome Icon Test</h1>
    
    <div class="test-result" id="testResult">
        <h3>Loading Test...</h3>
    </div>
    
    <div class="icon-test">
        <div class="icon-item">
            <i class="fas fa-flask"></i>
            <div class="icon-name">fa-flask</div>
        </div>
        <div class="icon-item">
            <i class="fas fa-heart"></i>
            <div class="icon-name">fa-heart</div>
        </div>
        <div class="icon-item">
            <i class="fas fa-shopping-cart"></i>
            <div class="icon-name">fa-shopping-cart</div>
        </div>
        <div class="icon-item">
            <i class="fas fa-shopping-bag"></i>
            <div class="icon-name">fa-shopping-bag</div>
        </div>
        <div class="icon-item">
            <i class="fas fa-user"></i>
            <div class="icon-name">fa-user</div>
        </div>
        <div class="icon-item">
            <i class="fas fa-search"></i>
            <div class="icon-name">fa-search</div>
        </div>
        <div class="icon-item">
            <i class="fas fa-bars"></i>
            <div class="icon-name">fa-bars</div>
        </div>
        <div class="icon-item">
            <i class="fas fa-times"></i>
            <div class="icon-name">fa-times</div>
        </div>
        <div class="icon-item">
            <i class="fas fa-home"></i>
            <div class="icon-name">fa-home</div>
        </div>
        <div class="icon-item">
            <i class="fas fa-star"></i>
            <div class="icon-name">fa-star</div>
        </div>
        <div class="icon-item">
            <i class="far fa-star"></i>
            <div class="icon-name">far fa-star</div>
        </div>
        <div class="icon-item">
            <i class="fas fa-eye"></i>
            <div class="icon-name">fa-eye</div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const testIcon = document.createElement('i');
                testIcon.className = 'fas fa-home';
                testIcon.style.position = 'absolute';
                testIcon.style.left = '-9999px';
                document.body.appendChild(testIcon);
                
                const computedStyle = window.getComputedStyle(testIcon, ':before');
                const content = computedStyle.getPropertyValue('content');
                const fontFamily = computedStyle.getPropertyValue('font-family');
                
                const testResult = document.getElementById('testResult');
                
                if (content && content !== 'none' && content !== '""' && fontFamily.includes('Font Awesome')) {
                    testResult.innerHTML = `
                        <h3 style="color: #48bb78;">✅ Font Awesome Loaded Successfully!</h3>
                        <p><strong>Content:</strong> ${content}</p>
                        <p><strong>Font Family:</strong> ${fontFamily}</p>
                    `;
                } else {
                    testResult.innerHTML = `
                        <h3 style="color: #f56565;">❌ Font Awesome Failed to Load</h3>
                        <p><strong>Content:</strong> ${content || 'none'}</p>
                        <p><strong>Font Family:</strong> ${fontFamily || 'none'}</p>
                        <p>Trying fallback...</p>
                    `;
                    
                    // Try fallback
                    const fallbackLink = document.createElement('link');
                    fallbackLink.rel = 'stylesheet';
                    fallbackLink.href = 'https://use.fontawesome.com/releases/v6.5.1/css/all.css';
                    document.head.appendChild(fallbackLink);
                }
                
                document.body.removeChild(testIcon);
            }, 1000);
        });
    </script>
</body>
</html>
