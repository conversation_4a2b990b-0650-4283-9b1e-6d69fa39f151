/**
 * Modern Framework - GSAP & Three.js Integration
 * Handles all modern animations and 3D effects
 */

class ModernFramework {
    constructor() {
        this.gsapLoaded = false;
        this.threeLoaded = false;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.animationId = null;
        
        this.init();
    }

    async init() {
        await this.loadLibraries();
        this.initGSAP();
        this.initThreeJS();
        this.setupScrollAnimations();
        this.setupInteractions();
        this.setupPageTransitions();
    }

    async loadLibraries() {
        try {
            // Load GSAP
            if (typeof gsap === 'undefined') {
                await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js');
                await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js');
                await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js');
            }
            this.gsapLoaded = true;
            console.log('GSAP loaded successfully');

            // Load Three.js
            if (typeof THREE === 'undefined') {
                await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js');
            }
            this.threeLoaded = true;
            console.log('Three.js loaded successfully');

        } catch (error) {
            console.error('Error loading libraries:', error);
        }
    }

    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    initGSAP() {
        if (!this.gsapLoaded || typeof gsap === 'undefined') return;

        // Register ScrollTrigger plugin
        gsap.registerPlugin(ScrollTrigger);
        gsap.registerPlugin(TextPlugin);

        // Set default ease
        gsap.defaults({
            ease: "power2.out",
            duration: 0.8
        });

        // Initial page load animation
        this.animatePageLoad();
    }

    animatePageLoad() {
        const tl = gsap.timeline();
        
        // Animate header
        tl.from('.header', {
            y: -100,
            opacity: 0,
            duration: 1,
            ease: "back.out(1.7)"
        });

        // Animate main content
        tl.from('.main', {
            y: 50,
            opacity: 0,
            duration: 0.8
        }, "-=0.5");

        // Animate cards with stagger
        tl.from('.card, .product-card, .glass', {
            y: 30,
            opacity: 0,
            duration: 0.6,
            stagger: 0.1
        }, "-=0.3");
    }

    setupScrollAnimations() {
        if (!this.gsapLoaded) return;

        // Fade in elements on scroll
        gsap.utils.toArray('.scroll-reveal').forEach(element => {
            gsap.fromTo(element, 
                {
                    opacity: 0,
                    y: 50
                },
                {
                    opacity: 1,
                    y: 0,
                    duration: 1,
                    scrollTrigger: {
                        trigger: element,
                        start: "top 80%",
                        end: "bottom 20%",
                        toggleActions: "play none none reverse"
                    }
                }
            );
        });

        // Parallax effects
        gsap.utils.toArray('.parallax-element').forEach(element => {
            gsap.to(element, {
                yPercent: -50,
                ease: "none",
                scrollTrigger: {
                    trigger: element,
                    start: "top bottom",
                    end: "bottom top",
                    scrub: true
                }
            });
        });

        // Text animations
        gsap.utils.toArray('.animate-text').forEach(element => {
            const text = element.textContent;
            element.innerHTML = text.split('').map(char => 
                `<span class="char">${char}</span>`
            ).join('');

            gsap.from(element.querySelectorAll('.char'), {
                opacity: 0,
                y: 20,
                duration: 0.05,
                stagger: 0.02,
                scrollTrigger: {
                    trigger: element,
                    start: "top 80%"
                }
            });
        });
    }

    setupInteractions() {
        // Magnetic effect for buttons
        document.querySelectorAll('.magnetic').forEach(element => {
            element.addEventListener('mousemove', (e) => {
                const rect = element.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;
                
                gsap.to(element, {
                    x: x * 0.3,
                    y: y * 0.3,
                    duration: 0.3,
                    ease: "power2.out"
                });
            });

            element.addEventListener('mouseleave', () => {
                gsap.to(element, {
                    x: 0,
                    y: 0,
                    duration: 0.5,
                    ease: "elastic.out(1, 0.3)"
                });
            });
        });

        // Hover animations
        document.querySelectorAll('.hover-lift').forEach(element => {
            element.addEventListener('mouseenter', () => {
                gsap.to(element, {
                    y: -10,
                    rotationX: 5,
                    duration: 0.3,
                    ease: "power2.out"
                });
            });

            element.addEventListener('mouseleave', () => {
                gsap.to(element, {
                    y: 0,
                    rotationX: 0,
                    duration: 0.3,
                    ease: "power2.out"
                });
            });
        });

        // Ripple effect
        document.querySelectorAll('.ripple').forEach(element => {
            element.addEventListener('click', (e) => {
                const rect = element.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const ripple = document.createElement('span');
                ripple.className = 'ripple-effect';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                element.appendChild(ripple);

                gsap.fromTo(ripple, 
                    {
                        scale: 0,
                        opacity: 1
                    },
                    {
                        scale: 4,
                        opacity: 0,
                        duration: 0.6,
                        ease: "power2.out",
                        onComplete: () => ripple.remove()
                    }
                );
            });
        });
    }

    setupPageTransitions() {
        // Smooth page transitions for SPA-like experience
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="/"], a[href^="./"]');
            if (link && !link.hasAttribute('target')) {
                e.preventDefault();
                this.transitionToPage(link.href);
            }
        });
    }

    transitionToPage(url) {
        const tl = gsap.timeline();
        
        tl.to('.main', {
            opacity: 0,
            x: -50,
            duration: 0.3,
            ease: "power2.in"
        })
        .call(() => {
            window.location.href = url;
        });
    }

    // Utility methods
    animateCounter(element, target, duration = 2) {
        if (!this.gsapLoaded) return;
        
        gsap.to({ value: 0 }, {
            value: target,
            duration: duration,
            ease: "power2.out",
            onUpdate: function() {
                element.textContent = Math.round(this.targets()[0].value);
            }
        });
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);

        const tl = gsap.timeline();
        tl.fromTo(notification, 
            { x: 300, opacity: 0 },
            { x: 0, opacity: 1, duration: 0.5, ease: "back.out(1.7)" }
        )
        .to(notification, 
            { x: 300, opacity: 0, duration: 0.3, delay: 3 }
        )
        .call(() => notification.remove());
    }

    // Three.js methods will be added in the next part
    initThreeJS() {
        // Three.js initialization will be implemented based on specific needs
        console.log('Three.js ready for implementation');
    }

    // Cleanup method
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        ScrollTrigger.killAll();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.modernFramework = new ModernFramework();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModernFramework;
}
