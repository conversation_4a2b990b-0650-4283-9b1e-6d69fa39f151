/**
 * Modern Forms - Enhanced CRUD Interface
 */

class ModernForms {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.formData = new FormData();
        this.validationRules = {};
        
        this.init();
    }

    init() {
        this.setupFormNavigation();
        this.setupValidation();
        this.setupFileUploads();
        this.setupCharacterCounters();
        this.setupFormSubmission();
        this.setupAutoSave();
    }

    setupFormNavigation() {
        // Step navigation
        document.querySelectorAll('.step').forEach(step => {
            step.addEventListener('click', (e) => {
                const stepNumber = parseInt(e.currentTarget.dataset.step);
                this.goToStep(stepNumber);
            });
        });

        // Navigation buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-next')) {
                e.preventDefault();
                this.nextStep();
            } else if (e.target.closest('.btn-prev')) {
                e.preventDefault();
                this.prevStep();
            }
        });
    }

    goToStep(stepNumber) {
        if (stepNumber < 1 || stepNumber > this.totalSteps) return;
        
        // Validate current step before moving
        if (stepNumber > this.currentStep && !this.validateCurrentStep()) {
            return;
        }

        // Hide current section
        document.querySelector('.modern-form-section.active')?.classList.remove('active');
        document.querySelector('.step.active')?.classList.remove('active');

        // Show new section
        document.querySelector(`[data-section="${stepNumber}"]`)?.classList.add('active');
        document.querySelector(`[data-step="${stepNumber}"]`)?.classList.add('active');

        this.currentStep = stepNumber;
        this.updateProgress();
        this.updateNavigationButtons();
    }

    nextStep() {
        if (this.currentStep < this.totalSteps) {
            this.goToStep(this.currentStep + 1);
        } else {
            this.submitForm();
        }
    }

    prevStep() {
        if (this.currentStep > 1) {
            this.goToStep(this.currentStep - 1);
        }
    }

    updateProgress() {
        const progress = (this.currentStep / this.totalSteps) * 100;
        const progressBar = document.getElementById('formProgress');
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
    }

    updateNavigationButtons() {
        const prevBtn = document.querySelector('.btn-prev');
        const nextBtn = document.querySelector('.btn-next');
        const submitBtn = document.querySelector('.btn-submit');

        if (prevBtn) {
            prevBtn.style.display = this.currentStep === 1 ? 'none' : 'flex';
        }

        if (this.currentStep === this.totalSteps) {
            if (nextBtn) nextBtn.style.display = 'none';
            if (submitBtn) submitBtn.style.display = 'flex';
        } else {
            if (nextBtn) nextBtn.style.display = 'flex';
            if (submitBtn) submitBtn.style.display = 'none';
        }
    }

    setupValidation() {
        // Real-time validation
        document.querySelectorAll('.modern-input, .modern-textarea, .modern-select').forEach(field => {
            field.addEventListener('blur', () => this.validateField(field));
            field.addEventListener('input', () => this.clearFieldError(field));
        });

        // Setup validation rules
        this.validationRules = {
            name: { required: true, minLength: 2, maxLength: 100 },
            description: { maxLength: 500 },
            category_id: { required: true },
            price: { required: true, min: 0.01 },
            stock_quantity: { required: true, min: 0 }
        };
    }

    validateField(field) {
        const name = field.name;
        const value = field.value.trim();
        const rules = this.validationRules[name];
        const validationDiv = document.getElementById(`${name}-validation`);

        if (!rules) return true;

        let isValid = true;
        let message = '';

        // Required validation
        if (rules.required && !value) {
            isValid = false;
            message = 'This field is required';
        }
        // Min length validation
        else if (rules.minLength && value.length < rules.minLength) {
            isValid = false;
            message = `Minimum ${rules.minLength} characters required`;
        }
        // Max length validation
        else if (rules.maxLength && value.length > rules.maxLength) {
            isValid = false;
            message = `Maximum ${rules.maxLength} characters allowed`;
        }
        // Min value validation
        else if (rules.min && parseFloat(value) < rules.min) {
            isValid = false;
            message = `Minimum value is ${rules.min}`;
        }

        // Update validation display
        if (validationDiv) {
            validationDiv.textContent = message;
            validationDiv.className = `field-validation ${isValid ? 'success' : 'error'}`;
        }

        // Update field styling
        const wrapper = field.closest('.input-wrapper, .textarea-wrapper, .select-wrapper');
        if (wrapper) {
            wrapper.style.borderColor = isValid ? 'var(--success-color)' : 'var(--error-color)';
        }

        return isValid;
    }

    clearFieldError(field) {
        const validationDiv = document.getElementById(`${field.name}-validation`);
        if (validationDiv) {
            validationDiv.textContent = '';
            validationDiv.className = 'field-validation';
        }

        const wrapper = field.closest('.input-wrapper, .textarea-wrapper, .select-wrapper');
        if (wrapper) {
            wrapper.style.borderColor = '';
        }
    }

    validateCurrentStep() {
        const currentSection = document.querySelector('.modern-form-section.active');
        const fields = currentSection.querySelectorAll('.modern-input, .modern-textarea, .modern-select');
        let isValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    setupFileUploads() {
        const uploadAreas = document.querySelectorAll('.file-upload-area');
        
        uploadAreas.forEach(area => {
            const fileInput = area.querySelector('.file-input');
            
            // Click to upload
            area.addEventListener('click', () => fileInput.click());
            
            // Drag and drop
            area.addEventListener('dragover', (e) => {
                e.preventDefault();
                area.classList.add('dragover');
            });
            
            area.addEventListener('dragleave', () => {
                area.classList.remove('dragover');
            });
            
            area.addEventListener('drop', (e) => {
                e.preventDefault();
                area.classList.remove('dragover');
                this.handleFiles(e.dataTransfer.files, area);
            });
            
            // File input change
            fileInput.addEventListener('change', (e) => {
                this.handleFiles(e.target.files, area);
            });
        });
    }

    handleFiles(files, uploadArea) {
        const previewContainer = uploadArea.nextElementSibling;
        
        Array.from(files).forEach(file => {
            if (file.type.startsWith('image/')) {
                this.createImagePreview(file, previewContainer);
            }
        });
    }

    createImagePreview(file, container) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            const previewItem = document.createElement('div');
            previewItem.className = 'preview-item';
            
            previewItem.innerHTML = `
                <img src="${e.target.result}" alt="Preview" class="preview-image">
                <button type="button" class="preview-remove" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            container.appendChild(previewItem);
        };
        
        reader.readAsDataURL(file);
    }

    setupCharacterCounters() {
        document.querySelectorAll('.modern-textarea').forEach(textarea => {
            const counter = document.getElementById(`${textarea.id.replace('description', 'desc')}-count`);
            
            if (counter) {
                const updateCounter = () => {
                    const count = textarea.value.length;
                    counter.textContent = count;
                    
                    // Color coding
                    const maxLength = parseInt(textarea.getAttribute('maxlength')) || 500;
                    const percentage = (count / maxLength) * 100;
                    
                    if (percentage > 90) {
                        counter.style.color = 'var(--error-color)';
                    } else if (percentage > 75) {
                        counter.style.color = 'var(--warning-color)';
                    } else {
                        counter.style.color = 'var(--text-muted)';
                    }
                };
                
                textarea.addEventListener('input', updateCounter);
                updateCounter(); // Initial count
            }
        });
    }

    setupFormSubmission() {
        const form = document.getElementById('addProductForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitForm();
            });
        }
    }

    async submitForm() {
        // Validate all fields
        if (!this.validateAllFields()) {
            window.alerts.error('Please fix all validation errors before submitting.');
            return;
        }

        // Show loading state
        this.setFormLoading(true);

        try {
            const formData = new FormData(document.getElementById('addProductForm'));
            
            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                window.alerts.success('Product created successfully!').then(() => {
                    window.location.href = result.redirect || '/admin/products';
                });
            } else {
                window.alerts.error(result.message || 'Failed to create product');
                this.displayFormErrors(result.errors);
            }
        } catch (error) {
            console.error('Form submission error:', error);
            window.alerts.error('An error occurred while submitting the form');
        } finally {
            this.setFormLoading(false);
        }
    }

    validateAllFields() {
        const fields = document.querySelectorAll('.modern-input, .modern-textarea, .modern-select');
        let isValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    displayFormErrors(errors) {
        if (!errors) return;

        Object.keys(errors).forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            const validationDiv = document.getElementById(`${fieldName}-validation`);
            
            if (validationDiv) {
                validationDiv.textContent = errors[fieldName];
                validationDiv.className = 'field-validation error';
            }
            
            if (field) {
                const wrapper = field.closest('.input-wrapper, .textarea-wrapper, .select-wrapper');
                if (wrapper) {
                    wrapper.style.borderColor = 'var(--error-color)';
                }
            }
        });
    }

    setFormLoading(loading) {
        const form = document.getElementById('addProductForm');
        if (loading) {
            form.classList.add('form-loading');
        } else {
            form.classList.remove('form-loading');
        }
    }

    setupAutoSave() {
        // Auto-save form data to localStorage
        const form = document.getElementById('addProductForm');
        if (form) {
            const saveKey = `form_autosave_${window.location.pathname}`;
            
            // Load saved data
            const savedData = localStorage.getItem(saveKey);
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);
                    Object.keys(data).forEach(key => {
                        const field = form.querySelector(`[name="${key}"]`);
                        if (field && field.type !== 'file') {
                            field.value = data[key];
                        }
                    });
                } catch (e) {
                    console.warn('Failed to load auto-saved data:', e);
                }
            }
            
            // Save data on input
            form.addEventListener('input', () => {
                const formData = new FormData(form);
                const data = {};
                
                for (let [key, value] of formData.entries()) {
                    if (typeof value === 'string') {
                        data[key] = value;
                    }
                }
                
                localStorage.setItem(saveKey, JSON.stringify(data));
            });
            
            // Clear saved data on successful submission
            form.addEventListener('submit', () => {
                localStorage.removeItem(saveKey);
            });
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.modern-form')) {
        window.modernForms = new ModernForms();
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModernForms;
}
