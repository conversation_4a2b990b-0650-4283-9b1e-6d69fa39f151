/**
 * Modern Admin Panel - Animations and Functionality
 */

class ModernAdmin {
    constructor() {
        this.charts = {};
        this.animationId = null;
        
        this.init();
    }

    async init() {
        await this.waitForLibraries();
        this.initTimeDisplay();
        this.initAnimations();
        this.initCounters();
        this.initMiniCharts();
        this.initQuickActions();
        this.initStaggerAnimations();
    }

    waitForLibraries() {
        return new Promise((resolve) => {
            const checkLibraries = () => {
                if (typeof gsap !== 'undefined') {
                    resolve();
                } else {
                    setTimeout(checkLibraries, 100);
                }
            };
            checkLibraries();
        });
    }

    initTimeDisplay() {
        const updateTime = () => {
            const now = new Date();
            const timeElement = document.getElementById('currentTime');
            const dateElement = document.getElementById('currentDate');
            
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('en-US', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            }
            
            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                });
            }
        };

        updateTime();
        setInterval(updateTime, 1000);
    }

    initAnimations() {
        if (typeof gsap === 'undefined') return;

        gsap.registerPlugin(ScrollTrigger);

        // Header animation
        const tl = gsap.timeline();
        
        tl.from('.modern-dashboard-header', {
            y: -50,
            opacity: 0,
            duration: 1,
            ease: "back.out(1.7)"
        })
        .from('.admin-avatar', {
            scale: 0,
            rotation: -180,
            duration: 1,
            ease: "elastic.out(1, 0.5)"
        }, "-=0.5")
        .from('.welcome-text > *', {
            x: -30,
            opacity: 0,
            duration: 0.8,
            stagger: 0.1,
            ease: "power2.out"
        }, "-=0.8")
        .from('.quick-action-btn', {
            scale: 0,
            opacity: 0,
            duration: 0.6,
            stagger: 0.1,
            ease: "back.out(1.7)"
        }, "-=0.5")
        .from('.time-display', {
            scale: 0.8,
            opacity: 0,
            duration: 0.8,
            ease: "power2.out"
        }, "-=0.3");

        // Floating animation for avatar glow
        gsap.to('.avatar-glow', {
            scale: 1.1,
            duration: 2,
            ease: "power1.inOut",
            yoyo: true,
            repeat: -1
        });

        // Background shapes animation
        gsap.utils.toArray('.admin-bg-shape').forEach((shape, index) => {
            gsap.to(shape, {
                rotation: 360,
                duration: 20 + (index * 5),
                ease: "none",
                repeat: -1
            });
        });
    }

    initCounters() {
        if (typeof gsap === 'undefined') return;

        // Animate stat counters
        gsap.utils.toArray('.stat-number').forEach(element => {
            const countElement = element.querySelector('[data-count]') || element;
            const target = parseInt(countElement.dataset.count || countElement.textContent.replace(/[^0-9]/g, ''));
            
            if (target > 0) {
                gsap.to({ value: 0 }, {
                    value: target,
                    duration: 2,
                    ease: "power2.out",
                    onUpdate: function() {
                        const currentValue = Math.round(this.targets()[0].value);
                        if (element.textContent.includes('$')) {
                            countElement.textContent = currentValue.toLocaleString();
                        } else {
                            countElement.textContent = currentValue.toLocaleString();
                        }
                    },
                    scrollTrigger: {
                        trigger: element,
                        start: "top 80%",
                        once: true
                    }
                });
            }
        });
    }

    initMiniCharts() {
        // Initialize mini charts for each stat card
        const chartConfigs = {
            usersChart: {
                data: [12, 19, 15, 25, 22, 30, 28],
                color: '#10b981'
            },
            productsChart: {
                data: [5, 8, 12, 15, 18, 20, 22],
                color: '#7E57C2'
            },
            ordersChart: {
                data: [8, 12, 18, 25, 22, 30, 35],
                color: '#2196F3'
            },
            revenueChart: {
                data: [100, 150, 200, 180, 250, 300, 350],
                color: '#10b981'
            },
            todayOrdersChart: {
                data: [2, 4, 6, 8, 10, 12, 15],
                color: '#ff9800'
            },
            todayRevenueChart: {
                data: [50, 80, 120, 100, 150, 200, 250],
                color: '#10b981'
            },
            pendingChart: {
                data: [10, 8, 6, 4, 5, 3, 2],
                color: '#ff9800'
            },
            stockChart: {
                data: [15, 12, 10, 8, 6, 4, 3],
                color: '#f44336'
            }
        };

        Object.keys(chartConfigs).forEach(chartId => {
            const canvas = document.getElementById(chartId);
            if (canvas) {
                this.createMiniChart(canvas, chartConfigs[chartId]);
            }
        });
    }

    createMiniChart(canvas, config) {
        const ctx = canvas.getContext('2d');
        const { data, color } = config;
        const width = canvas.width;
        const height = canvas.height;
        
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        
        // Set up gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, height);
        gradient.addColorStop(0, color + '80');
        gradient.addColorStop(1, color + '20');
        
        // Calculate points
        const points = data.map((value, index) => ({
            x: (index / (data.length - 1)) * width,
            y: height - (value / Math.max(...data)) * height
        }));
        
        // Draw area
        ctx.beginPath();
        ctx.moveTo(0, height);
        points.forEach(point => ctx.lineTo(point.x, point.y));
        ctx.lineTo(width, height);
        ctx.closePath();
        ctx.fillStyle = gradient;
        ctx.fill();
        
        // Draw line
        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);
        points.forEach(point => ctx.lineTo(point.x, point.y));
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // Draw points
        points.forEach(point => {
            ctx.beginPath();
            ctx.arc(point.x, point.y, 2, 0, Math.PI * 2);
            ctx.fillStyle = color;
            ctx.fill();
        });
    }

    initQuickActions() {
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleQuickAction(action);
            });
        });
    }

    handleQuickAction(action) {
        switch (action) {
            case 'add-product':
                window.location.href = '/admin/add-product';
                break;
            case 'view-orders':
                window.location.href = '/admin/orders';
                break;
            case 'analytics':
                window.location.href = '/admin/analytics';
                break;
            default:
                console.log('Unknown action:', action);
        }
    }

    initStaggerAnimations() {
        if (typeof gsap === 'undefined') return;

        // Stagger animation for stat cards
        const statCards = document.querySelectorAll('.modern-stat-card');
        
        gsap.fromTo(statCards,
            {
                opacity: 0,
                y: 50,
                scale: 0.9
            },
            {
                opacity: 1,
                y: 0,
                scale: 1,
                duration: 0.8,
                stagger: 0.1,
                ease: "back.out(1.7)",
                scrollTrigger: {
                    trigger: '.modern-stats-grid',
                    start: "top 80%",
                    onEnter: () => {
                        document.querySelector('.modern-stats-grid').classList.add('animate');
                    }
                }
            }
        );

        // Hover animations for stat cards
        statCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                gsap.to(card.querySelector('.stat-icon'), {
                    rotation: 360,
                    duration: 0.6,
                    ease: "power2.out"
                });
            });
        });
    }

    // Utility methods
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `admin-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 350px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            backdrop-filter: blur(20px);
            ${this.getNotificationStyle(type)}
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 4000);
    }

    getNotificationIcon(type) {
        switch (type) {
            case 'success': return 'check-circle';
            case 'error': return 'exclamation-circle';
            case 'warning': return 'exclamation-triangle';
            default: return 'info-circle';
        }
    }

    getNotificationStyle(type) {
        switch (type) {
            case 'success':
                return 'background: linear-gradient(135deg, #10b981, #059669);';
            case 'error':
                return 'background: linear-gradient(135deg, #ef4444, #dc2626);';
            case 'warning':
                return 'background: linear-gradient(135deg, #f59e0b, #d97706);';
            default:
                return 'background: linear-gradient(135deg, #7E57C2, #5E35B1);';
        }
    }

    // Cleanup method
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        ScrollTrigger.killAll();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.modernAdmin = new ModernAdmin();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.modernAdmin) {
        window.modernAdmin.destroy();
    }
});
