/* Modern Homepage Styles */

/* Hero Section */
.modern-hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--dark-bg);
    overflow: hidden;
}

.hero-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.hero-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    pointer-events: none;
}

.bg-element {
    position: absolute;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-3xl);
}

.bg-element-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: 10%;
    animation: float 8s ease-in-out infinite;
}

.bg-element-2 {
    width: 200px;
    height: 200px;
    bottom: 20%;
    left: 5%;
    animation: float 6s ease-in-out infinite reverse;
}

.bg-element-3 {
    width: 150px;
    height: 150px;
    top: 50%;
    left: 80%;
    animation: float 10s ease-in-out infinite;
    animation-delay: 2s;
}

.hero-content {
    position: relative;
    z-index: 3;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
    width: 100%;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--primary-purple-light);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-glass);
}

.hero-title {
    font-size: var(--font-size-6xl);
    font-weight: var(--font-weight-extrabold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.1;
    background: linear-gradient(135deg, var(--text-primary), var(--primary-purple-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.6;
    max-width: 500px;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-3xl);
}

.btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-2xl);
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    transition: all var(--transition-normal);
    overflow: hidden;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
    color: var(--white);
    box-shadow: var(--shadow-glow-purple);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-glow-purple), var(--shadow-xl);
    color: var(--white);
}

.btn-outline {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 2px solid var(--glass-border);
    color: var(--text-primary);
    box-shadow: var(--shadow-glass);
}

.btn-outline:hover {
    background: var(--primary-purple-ultra-light);
    border-color: var(--primary-purple);
    color: var(--primary-purple-light);
    transform: translateY(-3px);
}

.btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-slow);
}

.btn:hover .btn-glow {
    left: 100%;
}

.hero-stats {
    display: flex;
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-purple-light);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: var(--font-weight-medium);
}

.hero-visual-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-3d-scene {
    position: absolute;
    width: 400px;
    height: 400px;
    z-index: 1;
}

.hero-visual {
    position: relative;
    width: 350px;
    height: 350px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.visual-inner {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 80px;
    color: var(--primary-purple-light);
}

.visual-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    border-radius: 50%;
    z-index: -1;
    opacity: 0.7;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    font-size: 24px;
    z-index: 3;
}

.element-1 {
    top: 15%;
    left: 15%;
    animation-delay: 0s;
}

.element-2 {
    top: 70%;
    right: 15%;
    animation-delay: 1s;
}

.element-3 {
    bottom: 15%;
    left: 50%;
    animation-delay: 2s;
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-2xl);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    z-index: 3;
}

.scroll-mouse {
    width: 24px;
    height: 40px;
    border: 2px solid var(--text-muted);
    border-radius: 12px;
    position: relative;
}

.scroll-wheel {
    width: 4px;
    height: 8px;
    background: var(--primary-purple);
    border-radius: 2px;
    position: absolute;
    top: 6px;
    left: 50%;
    transform: translateX(-50%);
    animation: scroll-wheel 2s infinite;
}

@keyframes scroll-wheel {

    0%,
    100% {
        top: 6px;
        opacity: 1;
    }

    50% {
        top: 20px;
        opacity: 0.3;
    }
}

/* Modern Sections */
.modern-section {
    padding: var(--spacing-5xl) 0;
    position: relative;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--primary-purple-light);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-glass);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-4xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(135deg, var(--text-primary), var(--primary-purple-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.category-card {
    position: relative;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-2xl);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-normal);
    overflow: hidden;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.category-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.category-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, var(--primary-purple-ultra-light) 0%, transparent 50%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.category-card:hover .category-pattern {
    opacity: 1;
}

.category-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--primary-purple-light);
    position: relative;
    transition: all var(--transition-normal);
}

.category-icon i {
    position: relative;
    z-index: 2;
    transition: transform var(--transition-normal);
}

.category-card:hover .category-icon i {
    transform: scale(1.2) rotate(10deg);
}

.icon-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: var(--primary-purple-glow);
    border-radius: 50%;
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: 1;
    filter: blur(20px);
}

.category-card:hover .icon-glow {
    opacity: 1;
}

.category-name {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin: 0;
    transition: color var(--transition-normal);
}

.category-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
    transition: color var(--transition-normal);
}

.category-arrow {
    opacity: 0;
    transform: translateX(-10px);
    transition: all var(--transition-normal);
    color: var(--primary-purple-light);
}

.category-card:hover .category-arrow {
    opacity: 1;
    transform: translateX(0);
}

.category-hover-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-purple-ultra-light), transparent);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: 1;
}

.category-card:hover .category-hover-effect {
    opacity: 1;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-glow-purple), var(--shadow-2xl);
}

.category-card:hover .category-name {
    color: var(--primary-purple-light);
}

.category-card:hover .category-description {
    color: var(--text-primary);
}

/* Stagger Animation */
.stagger-container .stagger-item {
    opacity: 0;
    transform: translateY(30px);
    transition: all var(--transition-normal);
    transition-delay: var(--stagger-delay, 0s);
}

.stagger-container.animate .stagger-item {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-3xl);
        text-align: center;
    }

    .hero-title {
        font-size: var(--font-size-5xl);
    }

    .hero-visual {
        width: 280px;
        height: 280px;
    }

    .visual-inner {
        font-size: 60px;
    }
}

@media (max-width: 768px) {
    .modern-hero {
        min-height: 80vh;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        justify-content: center;
        gap: var(--spacing-lg);
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .bg-element {
        display: none;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-visual {
        width: 220px;
        height: 220px;
    }

    .visual-inner {
        font-size: 40px;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .category-card {
        min-height: 160px;
        padding: var(--spacing-xl);
    }
}