/**
 * Modern Homepage - 3D Effects and Animations
 */

class ModernHomepage {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.particles = [];
        this.animationId = null;
        
        this.init();
    }

    async init() {
        await this.waitForLibraries();
        this.initThreeJS();
        this.initGSAPAnimations();
        this.initScrollAnimations();
        this.initCounters();
        this.initStaggerAnimations();
    }

    waitForLibraries() {
        return new Promise((resolve) => {
            const checkLibraries = () => {
                if (typeof THREE !== 'undefined' && typeof gsap !== 'undefined') {
                    resolve();
                } else {
                    setTimeout(checkLibraries, 100);
                }
            };
            checkLibraries();
        });
    }

    initThreeJS() {
        const canvas = document.getElementById('hero-canvas');
        if (!canvas) return;

        // Scene setup
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ canvas, alpha: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x000000, 0);

        // Create floating particles
        this.createParticles();

        // Position camera
        this.camera.position.z = 5;

        // Start animation loop
        this.animate();

        // Handle resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    createParticles() {
        const particleCount = 100;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;
            
            // Position
            positions[i3] = (Math.random() - 0.5) * 20;
            positions[i3 + 1] = (Math.random() - 0.5) * 20;
            positions[i3 + 2] = (Math.random() - 0.5) * 20;

            // Color (purple theme)
            const color = new THREE.Color();
            color.setHSL(0.75 + Math.random() * 0.1, 0.7, 0.6);
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const material = new THREE.PointsMaterial({
            size: 0.05,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending
        });

        this.particles = new THREE.Points(geometry, material);
        this.scene.add(this.particles);
    }

    animate() {
        this.animationId = requestAnimationFrame(() => this.animate());

        // Rotate particles
        if (this.particles) {
            this.particles.rotation.x += 0.001;
            this.particles.rotation.y += 0.002;
        }

        this.renderer.render(this.scene, this.camera);
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    initGSAPAnimations() {
        // Register ScrollTrigger
        gsap.registerPlugin(ScrollTrigger);

        // Hero animations
        const tl = gsap.timeline();
        
        tl.from('.hero-badge', {
            y: 30,
            opacity: 0,
            duration: 1,
            ease: "back.out(1.7)"
        })
        .from('.hero-title', {
            y: 50,
            opacity: 0,
            duration: 1.2,
            ease: "power3.out"
        }, "-=0.5")
        .from('.hero-subtitle', {
            x: -50,
            opacity: 0,
            duration: 1,
            ease: "power2.out"
        }, "-=0.8")
        .from('.hero-actions .btn', {
            scale: 0.8,
            opacity: 0,
            duration: 0.8,
            stagger: 0.2,
            ease: "back.out(1.7)"
        }, "-=0.5")
        .from('.hero-stats .stat-item', {
            y: 30,
            opacity: 0,
            duration: 0.6,
            stagger: 0.1,
            ease: "power2.out"
        }, "-=0.3")
        .from('.hero-visual', {
            scale: 0.5,
            opacity: 0,
            rotation: -10,
            duration: 1.5,
            ease: "elastic.out(1, 0.5)"
        }, "-=1");

        // Floating animation for hero visual
        gsap.to('.hero-visual', {
            y: -20,
            duration: 3,
            ease: "power1.inOut",
            yoyo: true,
            repeat: -1
        });

        // Animate floating elements
        gsap.to('.element-1', {
            y: -15,
            x: 10,
            rotation: 360,
            duration: 4,
            ease: "power1.inOut",
            yoyo: true,
            repeat: -1
        });

        gsap.to('.element-2', {
            y: -20,
            x: -5,
            rotation: -360,
            duration: 5,
            ease: "power1.inOut",
            yoyo: true,
            repeat: -1,
            delay: 1
        });

        gsap.to('.element-3', {
            y: -10,
            x: 15,
            rotation: 180,
            duration: 6,
            ease: "power1.inOut",
            yoyo: true,
            repeat: -1,
            delay: 2
        });
    }

    initScrollAnimations() {
        // Scroll-triggered animations
        gsap.utils.toArray('.scroll-reveal').forEach(element => {
            gsap.fromTo(element, 
                {
                    opacity: 0,
                    y: 50
                },
                {
                    opacity: 1,
                    y: 0,
                    duration: 1,
                    scrollTrigger: {
                        trigger: element,
                        start: "top 80%",
                        end: "bottom 20%",
                        toggleActions: "play none none reverse"
                    }
                }
            );
        });

        // Text animation
        gsap.utils.toArray('.animate-text').forEach(element => {
            const text = element.textContent;
            element.innerHTML = text.split('').map(char => 
                char === ' ' ? ' ' : `<span class="char">${char}</span>`
            ).join('');

            gsap.from(element.querySelectorAll('.char'), {
                opacity: 0,
                y: 20,
                duration: 0.05,
                stagger: 0.02,
                scrollTrigger: {
                    trigger: element,
                    start: "top 80%"
                }
            });
        });

        // Parallax background elements
        gsap.utils.toArray('.bg-element').forEach((element, index) => {
            gsap.to(element, {
                yPercent: -30 - (index * 10),
                ease: "none",
                scrollTrigger: {
                    trigger: ".modern-hero",
                    start: "top bottom",
                    end: "bottom top",
                    scrub: true
                }
            });
        });
    }

    initCounters() {
        // Animate counters
        gsap.utils.toArray('.stat-number').forEach(element => {
            const target = parseInt(element.dataset.count);
            
            gsap.to({ value: 0 }, {
                value: target,
                duration: 2,
                ease: "power2.out",
                onUpdate: function() {
                    element.textContent = Math.round(this.targets()[0].value);
                },
                scrollTrigger: {
                    trigger: element,
                    start: "top 80%",
                    once: true
                }
            });
        });
    }

    initStaggerAnimations() {
        // Stagger animation for categories
        const staggerContainers = document.querySelectorAll('.stagger-container');
        
        staggerContainers.forEach(container => {
            const items = container.querySelectorAll('.stagger-item');
            
            gsap.fromTo(items,
                {
                    opacity: 0,
                    y: 30
                },
                {
                    opacity: 1,
                    y: 0,
                    duration: 0.8,
                    stagger: 0.1,
                    ease: "power2.out",
                    scrollTrigger: {
                        trigger: container,
                        start: "top 80%",
                        onEnter: () => container.classList.add('animate')
                    }
                }
            );
        });
    }

    // Cleanup method
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        if (this.renderer) {
            this.renderer.dispose();
        }
        ScrollTrigger.killAll();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.modernHomepage = new ModernHomepage();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.modernHomepage) {
        window.modernHomepage.destroy();
    }
});
