/* Modern Forms & CRUD Interface */

/* Modern Admin Container */
.modern-admin-container {
    position: relative;
    min-height: 100vh;
    padding: var(--spacing-xl) 0;
}

/* Modern Admin Header */
.modern-admin-header {
    margin-bottom: var(--spacing-3xl);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    position: relative;
    overflow: hidden;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-2xl);
}

.header-title-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.title-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--primary-purple-light);
    position: relative;
}

.admin-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    background: linear-gradient(135deg, var(--text-primary), var(--primary-purple-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.admin-subtitle {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
    margin: var(--spacing-xs) 0 0 0;
}

.btn-modern {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
}

.btn-modern:hover {
    background: var(--primary-purple-ultra-light);
    color: var(--primary-purple-light);
    border-color: var(--primary-purple);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-purple);
}

.btn-secondary {
    background: var(--glass-bg-dark);
}

/* Modern Form Card */
.modern-form-card {
    border-radius: var(--radius-2xl);
    padding: var(--spacing-3xl);
    position: relative;
    overflow: hidden;
}

/* Form Progress */
.form-progress {
    margin-bottom: var(--spacing-3xl);
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--dark-surface);
    border-radius: var(--radius-full);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-purple), var(--primary-purple-light));
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
    width: 25%;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-md);
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    cursor: pointer;
    flex: 1;
    text-align: center;
}

.step.active {
    background: var(--primary-purple-ultra-light);
    color: var(--primary-purple-light);
}

.step i {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
}

.step span {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

/* Form Sections */
.form-sections {
    position: relative;
}

.modern-form-section {
    display: none;
    animation: fadeInUp 0.6s ease-out;
}

.modern-form-section.active {
    display: block;
}

.section-header {
    margin-bottom: var(--spacing-2xl);
    text-align: center;
}

.section-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.section-description {
    color: var(--text-secondary);
    font-size: var(--font-size-md);
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.modern-form-group.full-width {
    grid-column: 1 / -1;
}

/* Modern Form Groups */
.modern-form-group {
    margin-bottom: var(--spacing-xl);
}

.modern-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.label-tooltip {
    color: var(--text-muted);
    cursor: help;
    transition: color var(--transition-fast);
}

.label-tooltip:hover {
    color: var(--primary-purple-light);
}

/* Input Wrappers */
.input-wrapper,
.textarea-wrapper,
.select-wrapper {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.modern-input,
.modern-textarea,
.modern-select {
    width: 100%;
    background: transparent;
    border: none;
    padding: var(--spacing-lg);
    color: var(--text-primary);
    font-size: var(--font-size-md);
    transition: all var(--transition-normal);
}

.modern-input::placeholder,
.modern-textarea::placeholder {
    color: var(--text-muted);
}

.modern-input:focus,
.modern-textarea:focus,
.modern-select:focus {
    outline: none;
}

.input-focus-border,
.textarea-focus-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-purple), var(--primary-purple-light));
    transition: width var(--transition-normal);
}

.modern-input:focus + .input-focus-border,
.modern-textarea:focus + .textarea-focus-border {
    width: 100%;
}

/* Select Styling */
.modern-select {
    appearance: none;
    cursor: pointer;
    padding-right: 50px;
}

.select-arrow {
    position: absolute;
    right: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
    transition: all var(--transition-normal);
}

.select-wrapper:hover .select-arrow {
    color: var(--primary-purple-light);
    transform: translateY(-50%) rotate(180deg);
}

/* Character Count */
.character-count {
    text-align: right;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-top: var(--spacing-sm);
}

/* Field Validation */
.field-validation {
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
    min-height: 20px;
}

.field-validation.error {
    color: var(--error-color);
}

.field-validation.success {
    color: var(--success-color);
}

/* File Upload */
.file-upload-area {
    border: 2px dashed var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-3xl);
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.file-upload-area:hover {
    border-color: var(--primary-purple);
    background: var(--primary-purple-ultra-light);
}

.file-upload-area.dragover {
    border-color: var(--primary-purple-light);
    background: var(--primary-purple-soft);
    transform: scale(1.02);
}

.upload-icon {
    font-size: var(--font-size-4xl);
    color: var(--primary-purple-light);
    margin-bottom: var(--spacing-lg);
}

.upload-text {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.upload-hint {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* Image Preview */
.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.preview-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--dark-surface);
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-remove {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    width: 24px;
    height: 24px;
    background: var(--error-color);
    color: var(--white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    transition: all var(--transition-fast);
}

.preview-remove:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-glow-error);
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-3xl);
    padding-top: var(--spacing-2xl);
    border-top: 1px solid var(--glass-border);
}

.form-navigation {
    display: flex;
    gap: var(--spacing-md);
}

.btn-nav {
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-full);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
}

.btn-prev {
    background: var(--glass-bg);
    color: var(--text-secondary);
    border: 1px solid var(--glass-border);
}

.btn-next,
.btn-submit {
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
    color: var(--white);
    box-shadow: var(--shadow-glow-purple);
}

.btn-nav:hover {
    transform: translateY(-2px);
}

.btn-next:hover,
.btn-submit:hover {
    box-shadow: var(--shadow-glow-purple), var(--shadow-xl);
}

/* Loading State */
.form-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border: 3px solid var(--glass-border);
    border-top: 3px solid var(--primary-purple);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
    z-index: 10;
}
