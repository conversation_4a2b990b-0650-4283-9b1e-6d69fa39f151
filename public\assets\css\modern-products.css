/* Modern Products Page Styles */

/* Products Header */
.modern-products-header {
    position: relative;
    padding: var(--spacing-4xl) 0 var(--spacing-3xl);
    background: var(--dark-bg);
    overflow: hidden;
}

.products-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.bg-shape {
    position: absolute;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-3xl);
}

.bg-shape-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    right: 10%;
    animation: float 8s ease-in-out infinite;
}

.bg-shape-2 {
    width: 150px;
    height: 150px;
    bottom: 30%;
    left: 5%;
    animation: float 6s ease-in-out infinite reverse;
}

.bg-shape-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 30%;
    animation: float 10s ease-in-out infinite;
    animation-delay: 2s;
}

.products-header-content {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-2xl);
    gap: var(--spacing-2xl);
}

.title-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--primary-purple-light);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-glass);
}

.page-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    background: linear-gradient(135deg, var(--text-primary), var(--primary-purple-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.products-count {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.count-number {
    font-weight: var(--font-weight-bold);
    color: var(--primary-purple-light);
    font-size: var(--font-size-xl);
}

.products-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

/* View Toggle */
.view-toggle {
    display: flex;
    border-radius: var(--radius-full);
    padding: var(--spacing-xs);
    overflow: hidden;
}

.view-btn {
    background: transparent;
    border: none;
    padding: var(--spacing-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-normal);
    border-radius: var(--radius-full);
    position: relative;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn.active {
    background: var(--primary-purple);
    color: var(--white);
    box-shadow: var(--shadow-glow-purple);
}

.view-btn:hover:not(.active) {
    background: var(--primary-purple-ultra-light);
    color: var(--primary-purple-light);
}

.btn-tooltip {
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--dark-card);
    color: var(--text-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-fast);
    z-index: 10;
}

.view-btn:hover .btn-tooltip {
    opacity: 1;
}

/* Modern Select */
.sort-select-container {
    position: relative;
    border-radius: var(--radius-full);
    overflow: hidden;
}

.modern-select {
    background: transparent;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    padding-right: 50px;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    appearance: none;
    min-width: 200px;
}

.modern-select:focus {
    outline: none;
}

.select-arrow {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-purple-light);
    pointer-events: none;
    transition: transform var(--transition-normal);
}

.sort-select-container:hover .select-arrow {
    transform: translateY(-50%) rotate(180deg);
}

/* Mobile Filters Toggle */
.filters-toggle {
    display: none;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-full);
    border: none;
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
}

/* Modern Filters Sidebar */
.modern-filters {
    padding: var(--spacing-2xl);
    border-radius: var(--radius-2xl);
    position: sticky;
    top: var(--spacing-xl);
    max-height: calc(100vh - var(--spacing-2xl));
    overflow-y: auto;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--glass-border);
}

.filters-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.filters-title i {
    color: var(--primary-purple-light);
}

.clear-filters {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.clear-filters:hover {
    background: var(--primary-purple-ultra-light);
    color: var(--primary-purple-light);
    border-color: var(--primary-purple);
}

/* Filter Groups */
.filter-group {
    margin-bottom: var(--spacing-2xl);
}

.filter-title {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.filter-title i {
    color: var(--primary-purple-light);
    font-size: var(--font-size-sm);
}

/* Modern Search Input */
.modern-search-input {
    position: relative;
    border-radius: var(--radius-full);
    overflow: hidden;
}

.modern-search-input .search-input {
    width: 100%;
    background: transparent;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    padding-right: 50px;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.modern-search-input .search-input::placeholder {
    color: var(--text-muted);
}

.modern-search-input .search-input:focus {
    outline: none;
}

.modern-search-input .search-btn {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-purple);
    border: none;
    color: var(--white);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.modern-search-input .search-btn:hover {
    background: var(--primary-purple-dark);
    transform: translateY(-50%) scale(1.1);
}

/* Modern Radio Buttons */
.modern-radio {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    margin-bottom: var(--spacing-sm);
    overflow: hidden;
}

.modern-radio input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--glass-border);
    border-radius: 50%;
    position: relative;
    transition: all var(--transition-normal);
}

.radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 10px;
    background: var(--primary-purple);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform var(--transition-normal);
}

.modern-radio input[type="radio"]:checked+.radio-custom {
    border-color: var(--primary-purple);
    box-shadow: var(--shadow-glow-purple);
}

.modern-radio input[type="radio"]:checked+.radio-custom::after {
    transform: translate(-50%, -50%) scale(1);
}

.radio-label {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-normal);
}

.option-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-purple-ultra-light);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.modern-radio:hover .option-glow {
    opacity: 1;
}

.modern-radio input[type="radio"]:checked~.option-glow {
    opacity: 1;
}

.modern-radio:hover .radio-label,
.modern-radio input[type="radio"]:checked~.radio-label {
    color: var(--primary-purple-light);
}

/* Modern Price Filter */
.modern-price-filter {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.price-inputs {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.price-input-group {
    position: relative;
    flex: 1;
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.modern-input {
    width: 100%;
    background: transparent;
    border: none;
    padding: var(--spacing-md);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    text-align: center;
}

.modern-input::placeholder {
    color: var(--text-muted);
}

.modern-input:focus {
    outline: none;
}

.input-label {
    position: absolute;
    top: -8px;
    left: var(--spacing-sm);
    background: var(--dark-bg-solid);
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    padding: 0 var(--spacing-xs);
    border-radius: var(--radius-sm);
}

.price-separator {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.btn-modern {
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
    border: none;
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    box-shadow: var(--shadow-glow-purple);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-purple), var(--shadow-xl);
}

/* Products Grid */
.products-grid {
    display: grid;
    gap: var(--spacing-xl);
}

.products-grid.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

.products-grid.list-view {
    grid-template-columns: 1fr;
}

/* Modern Product Cards */
.modern-product-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
    box-shadow: var(--shadow-glass);
}

.modern-product-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-glow-purple), var(--shadow-2xl);
}

.product-image-container {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    background: var(--dark-surface);
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.modern-product-card:hover .product-image {
    transform: scale(1.1);
}

.product-badges {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    z-index: 2;
}

.product-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    color: var(--white);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.badge-sale {
    background: linear-gradient(135deg, var(--error-color), #dc2626);
    animation: pulse-glow 2s infinite;
}

.badge-new {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.badge-featured {
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
}

.product-actions-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    opacity: 0;
    transition: all var(--transition-normal);
}

.modern-product-card:hover .product-actions-overlay {
    opacity: 1;
}

.action-btn {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    color: var(--white);
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    transform: translateY(20px);
}

.modern-product-card:hover .action-btn {
    transform: translateY(0);
}

.action-btn:nth-child(1) {
    transition-delay: 0.1s;
}

.action-btn:nth-child(2) {
    transition-delay: 0.2s;
}

.action-btn:nth-child(3) {
    transition-delay: 0.3s;
}

.action-btn:hover {
    background: var(--primary-purple);
    transform: translateY(0) scale(1.1);
    box-shadow: var(--shadow-glow-purple);
}

.product-content {
    padding: var(--spacing-xl);
}

.product-category {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.product-category i {
    color: var(--primary-purple-light);
}

.product-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-title:hover {
    color: var(--primary-purple-light);
}

.product-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.stars {
    display: flex;
    gap: 2px;
}

.stars i {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.stars i.filled {
    color: #fbbf24;
}

.rating-count {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.product-price {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.price-current {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-purple-light);
}

.price-original {
    font-size: var(--font-size-md);
    color: var(--text-muted);
    text-decoration: line-through;
}

.price-discount {
    background: var(--success-color);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
}

.product-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.add-to-cart-btn {
    flex: 1;
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
    border: none;
    color: var(--white);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.add-to-cart-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-purple);
}

.wishlist-btn {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.wishlist-btn:hover {
    background: var(--primary-purple-ultra-light);
    color: var(--primary-purple-light);
    border-color: var(--primary-purple);
}

/* List View Styles */
.products-grid.list-view .modern-product-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: var(--spacing-xl);
}

.products-grid.list-view .product-image-container {
    width: 200px;
    height: 200px;
    flex-shrink: 0;
    margin-right: var(--spacing-xl);
}

.products-grid.list-view .product-content {
    flex: 1;
    padding: 0;
}

.products-grid.list-view .product-actions {
    margin-top: var(--spacing-lg);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-3xl);
}

.pagination-btn {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: all var(--transition-normal);
    min-width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-medium);
}

.pagination-btn:hover {
    background: var(--primary-purple-ultra-light);
    color: var(--primary-purple-light);
    border-color: var(--primary-purple);
    transform: translateY(-2px);
}

.pagination-btn.active {
    background: var(--primary-purple);
    color: var(--white);
    border-color: var(--primary-purple);
    box-shadow: var(--shadow-glow-purple);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Loading States */
.products-loading {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.product-skeleton {
    background: var(--glass-bg);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    animation: shimmer 2s infinite;
}

.skeleton-image {
    aspect-ratio: 1;
    background: var(--dark-surface);
}

.skeleton-content {
    padding: var(--spacing-xl);
}

.skeleton-line {
    height: 16px;
    background: var(--dark-surface);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
}

.skeleton-line.short {
    width: 60%;
}

.skeleton-line.medium {
    width: 80%;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .products-grid.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .products-header-content {
        flex-direction: column;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .products-actions {
        justify-content: center;
    }
}

@media (max-width: 1024px) {
    .products-layout {
        grid-template-columns: 1fr;
    }

    .modern-filters {
        position: fixed;
        top: 0;
        left: -100%;
        width: 320px;
        height: 100vh;
        z-index: var(--z-modal);
        transition: left var(--transition-normal);
        border-radius: 0;
        border-top-right-radius: var(--radius-2xl);
        border-bottom-right-radius: var(--radius-2xl);
    }

    .modern-filters.active {
        left: 0;
    }

    .filters-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: var(--z-modal-backdrop);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .filters-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .filters-toggle {
        display: flex;
    }

    .bg-shape {
        display: none;
    }
}

@media (max-width: 768px) {
    .modern-products-header {
        padding: var(--spacing-2xl) 0;
    }

    .page-title {
        font-size: var(--font-size-3xl);
    }

    .products-actions {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .products-grid.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: var(--spacing-lg);
    }

    .products-grid.list-view .modern-product-card {
        flex-direction: column;
        text-align: center;
    }

    .products-grid.list-view .product-image-container {
        width: 100%;
        margin-right: 0;
        margin-bottom: var(--spacing-lg);
    }

    .modern-product-card {
        border-radius: var(--radius-xl);
    }

    .product-content {
        padding: var(--spacing-lg);
    }

    .pagination {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .pagination-btn {
        min-width: 40px;
        height: 40px;
        padding: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .products-grid.grid-view {
        grid-template-columns: 1fr;
    }

    .modern-filters {
        width: 100%;
        border-radius: 0;
    }

    .product-actions {
        flex-direction: column;
    }

    .add-to-cart-btn,
    .wishlist-btn {
        width: 100%;
        height: 48px;
    }

    .products-header-content {
        padding: var(--spacing-lg);
    }

    .title-badge {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs) var(--spacing-md);
    }

    .page-title {
        font-size: var(--font-size-2xl);
    }
}

/* Dark/Light Theme Adjustments */
[data-theme="light"] .modern-product-card {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .product-skeleton {
    background: rgba(255, 255, 255, 0.6);
}

[data-theme="light"] .skeleton-image,
[data-theme="light"] .skeleton-line {
    background: rgba(0, 0, 0, 0.1);
}

/* Animation Classes */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stagger-delay-1 {
    animation-delay: 0.1s;
}

.stagger-delay-2 {
    animation-delay: 0.2s;
}

.stagger-delay-3 {
    animation-delay: 0.3s;
}

.stagger-delay-4 {
    animation-delay: 0.4s;
}

.stagger-delay-5 {
    animation-delay: 0.5s;
}