/* Modern Animations & GSAP Integration */

/* GSAP Animation Classes */
.gsap-fade-in {
    opacity: 0;
    transform: translateY(30px);
}

.gsap-fade-in-left {
    opacity: 0;
    transform: translateX(-50px);
}

.gsap-fade-in-right {
    opacity: 0;
    transform: translateX(50px);
}

.gsap-scale-in {
    opacity: 0;
    transform: scale(0.8);
}

.gsap-rotate-in {
    opacity: 0;
    transform: rotate(-10deg) scale(0.9);
}

/* Scroll Trigger Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: none;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Loading Animations */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px var(--primary-purple-glow);
    }
    50% {
        box-shadow: 0 0 40px var(--primary-purple-glow), 0 0 60px var(--primary-purple-glow);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes rotate-3d {
    0% {
        transform: rotateY(0deg);
    }
    100% {
        transform: rotateY(360deg);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Interactive Hover Effects */
.hover-glow {
    position: relative;
    transition: all var(--transition-normal);
}

.hover-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-purple-glow);
    border-radius: inherit;
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
    filter: blur(20px);
}

.hover-glow:hover::before {
    opacity: 1;
}

/* Magnetic Effect */
.magnetic {
    transition: transform var(--transition-fast);
    cursor: pointer;
}

/* Ripple Effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Parallax Elements */
.parallax-element {
    will-change: transform;
}

/* Three.js Canvas Styles */
.threejs-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.threejs-interactive {
    pointer-events: auto;
    cursor: pointer;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--dark-surface) 25%, var(--dark-elevated) 50%, var(--dark-surface) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
    border-radius: var(--radius-md);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--dark-surface);
    border-top: 3px solid var(--primary-purple);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Micro-interactions */
.micro-bounce {
    animation: micro-bounce 0.6s ease-in-out;
}

@keyframes micro-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.micro-shake {
    animation: micro-shake 0.5s ease-in-out;
}

@keyframes micro-shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* Stagger Animation Classes */
.stagger-item {
    opacity: 0;
    transform: translateY(30px);
}

.stagger-item.animate {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Page Transition Effects */
.page-transition-enter {
    opacity: 0;
    transform: translateX(100px);
}

.page-transition-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-exit {
    opacity: 1;
    transform: translateX(0);
}

.page-transition-exit-active {
    opacity: 0;
    transform: translateX(-100px);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Utility Classes for Animations */
.animate-pulse {
    animation: pulse-glow 2s infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-rotate {
    animation: rotate-3d 10s linear infinite;
}

/* Performance Optimizations */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

.smooth-scroll {
    scroll-behavior: smooth;
}

/* Responsive Animation Controls */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .parallax-element {
        transform: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .glass,
    .glass-dark {
        background: var(--dark-card);
        backdrop-filter: none;
    }
}
