/* Modern Admin Panel Styles */

/* Admin Background Elements */
.modern-admin-dashboard {
    position: relative;
    min-height: 100vh;
    padding: var(--spacing-xl) 0;
}

.admin-bg-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.admin-bg-shape {
    position: absolute;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-3xl);
}

.admin-bg-shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: 5%;
    animation: float 12s ease-in-out infinite;
}

.admin-bg-shape-2 {
    width: 200px;
    height: 200px;
    bottom: 20%;
    left: 10%;
    animation: float 8s ease-in-out infinite reverse;
}

.admin-bg-shape-3 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 30%;
    animation: float 10s ease-in-out infinite;
    animation-delay: 3s;
}

/* Modern Dashboard Header */
.modern-dashboard-header {
    margin-bottom: var(--spacing-3xl);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    position: relative;
    overflow: hidden;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-2xl);
}

.welcome-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.admin-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--white);
    box-shadow: var(--shadow-glow-purple);
}

.avatar-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: var(--primary-purple-glow);
    border-radius: 50%;
    z-index: -1;
    filter: blur(20px);
    animation: pulse-glow 3s infinite;
}

.welcome-text {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.dashboard-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    background: linear-gradient(135deg, var(--text-primary), var(--primary-purple-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-message {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin: 0;
}

.user-name {
    color: var(--primary-purple-light);
    font-weight: var(--font-weight-semibold);
}

.last-login {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.last-login i {
    color: var(--primary-purple-light);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.quick-actions {
    display: flex;
    gap: var(--spacing-md);
}

.quick-action-btn {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
}

.quick-action-btn:hover {
    background: var(--primary-purple-ultra-light);
    color: var(--primary-purple-light);
    border-color: var(--primary-purple);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-purple);
}

.time-display {
    padding: var(--spacing-lg);
    border-radius: var(--radius-xl);
    text-align: center;
    min-width: 120px;
}

.current-time {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-purple-light);
    font-family: var(--font-mono);
}

.current-date {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* Modern Statistics Grid */
.modern-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-4xl);
}

.modern-stat-card {
    position: relative;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-2xl);
    transition: all var(--transition-normal);
    overflow: hidden;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.modern-stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-glow-purple), var(--shadow-2xl);
}

.stat-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.stat-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    transition: opacity var(--transition-normal);
}

.stat-pattern-users {
    background: radial-gradient(circle at 80% 20%, var(--success-color) 0%, transparent 50%);
}

.stat-pattern-products {
    background: radial-gradient(circle at 80% 20%, var(--primary-purple) 0%, transparent 50%);
}

.stat-pattern-orders {
    background: radial-gradient(circle at 80% 20%, var(--info-color) 0%, transparent 50%);
}

.stat-pattern-revenue {
    background: radial-gradient(circle at 80% 20%, var(--success-color) 0%, transparent 50%);
}

.stat-pattern-today {
    background: radial-gradient(circle at 80% 20%, var(--warning-color) 0%, transparent 50%);
}

.stat-pattern-today-revenue {
    background: radial-gradient(circle at 80% 20%, var(--success-color) 0%, transparent 50%);
}

.stat-pattern-pending {
    background: radial-gradient(circle at 80% 20%, var(--warning-color) 0%, transparent 50%);
}

.stat-pattern-stock {
    background: radial-gradient(circle at 80% 20%, var(--error-color) 0%, transparent 50%);
}

.modern-stat-card:hover .stat-pattern {
    opacity: 0.2;
}

.stat-icon {
    position: relative;
    z-index: 2;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--primary-purple-light);
    margin-bottom: var(--spacing-lg);
    transition: all var(--transition-normal);
}

.modern-stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
}

.icon-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
    filter: blur(15px);
}

.modern-stat-card:hover .icon-glow {
    opacity: 1;
}

.stat-content {
    position: relative;
    z-index: 2;
    flex-grow: 1;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    font-family: var(--font-mono);
}

.stat-label {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-md) 0;
    font-weight: var(--font-weight-medium);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--success-color);
    font-weight: var(--font-weight-medium);
}

.stat-trend.urgent {
    color: var(--warning-color);
}

.stat-trend i {
    font-size: var(--font-size-xs);
}

.stat-chart {
    position: absolute;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    opacity: 0.7;
    transition: opacity var(--transition-normal);
}

.modern-stat-card:hover .stat-chart {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .modern-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xl);
    }
    
    .quick-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .modern-stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .modern-stat-card {
        min-height: 150px;
        padding: var(--spacing-xl);
    }
    
    .welcome-section {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-lg);
    }
    
    .admin-avatar {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }
    
    .dashboard-title {
        font-size: var(--font-size-3xl);
    }
    
    .quick-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .quick-action-btn {
        justify-content: center;
        width: 100%;
    }
    
    .admin-bg-shape {
        display: none;
    }
}

@media (max-width: 480px) {
    .modern-dashboard-header {
        padding: var(--spacing-lg);
    }
    
    .dashboard-title {
        font-size: var(--font-size-2xl);
    }
    
    .modern-stat-card {
        padding: var(--spacing-lg);
        min-height: 120px;
    }
    
    .stat-number {
        font-size: var(--font-size-2xl);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
}
