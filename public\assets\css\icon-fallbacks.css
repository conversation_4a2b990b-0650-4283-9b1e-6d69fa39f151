/* Icon Fallbacks CSS - Ensures icons display even if Font Awesome fails to load */

/* Base icon styles */
.fas, .far, .fab, .fa {
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Fallback using Unicode characters when Font Awesome doesn't load */
.fa-flask::before { content: "🧪"; }
.fa-heart::before { content: "❤️"; }
.fa-shopping-cart::before { content: "🛒"; }
.fa-shopping-bag::before { content: "🛍️"; }
.fa-user::before { content: "👤"; }
.fa-search::before { content: "🔍"; }
.fa-bars::before { content: "☰"; }
.fa-times::before { content: "✕"; }
.fa-home::before { content: "🏠"; }
.fa-info-circle::before { content: "ℹ️"; }
.fa-star::before { content: "⭐"; }
.fa-eye::before { content: "👁️"; }
.fa-plus::before { content: "+"; }
.fa-minus::before { content: "-"; }
.fa-trash::before { content: "🗑️"; }
.fa-edit::before { content: "✏️"; }
.fa-check::before { content: "✓"; }
.fa-spinner::before { content: "⟳"; }
.fa-moon::before { content: "🌙"; }
.fa-sun::before { content: "☀️"; }
.fa-envelope::before { content: "✉️"; }
.fa-phone::before { content: "📞"; }
.fa-map-marker-alt::before { content: "📍"; }
.fa-facebook-f::before { content: "f"; }
.fa-twitter::before { content: "🐦"; }
.fa-instagram::before { content: "📷"; }
.fa-linkedin::before { content: "💼"; }
.fa-youtube::before { content: "📺"; }

/* Alternative text-based fallbacks */
.icon-fallback .fa-flask::before { content: "Flask"; font-size: 0.8em; }
.icon-fallback .fa-heart::before { content: "♥"; }
.icon-fallback .fa-shopping-cart::before { content: "Cart"; font-size: 0.8em; }
.icon-fallback .fa-shopping-bag::before { content: "Shop"; font-size: 0.8em; }
.icon-fallback .fa-user::before { content: "User"; font-size: 0.8em; }
.icon-fallback .fa-search::before { content: "Search"; font-size: 0.8em; }
.icon-fallback .fa-bars::before { content: "Menu"; font-size: 0.8em; }
.icon-fallback .fa-times::before { content: "×"; }
.icon-fallback .fa-home::before { content: "Home"; font-size: 0.8em; }
.icon-fallback .fa-info-circle::before { content: "Info"; font-size: 0.8em; }
.icon-fallback .fa-star::before { content: "★"; }
.icon-fallback .fa-eye::before { content: "View"; font-size: 0.8em; }

/* Spinning animation */
.fa-spin {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(359deg); }
}

/* Size utilities */
.fa-xs { font-size: 0.75em; }
.fa-sm { font-size: 0.875em; }
.fa-lg { font-size: 1.33333em; line-height: 0.75em; vertical-align: -0.0667em; }
.fa-xl { font-size: 1.5em; line-height: 0.6667em; vertical-align: -0.075em; }
.fa-2x { font-size: 2em; }
.fa-3x { font-size: 3em; }
.fa-4x { font-size: 4em; }
.fa-5x { font-size: 5em; }

/* Ensure icons are always visible */
i[class*="fa-"] {
    min-width: 1em;
    text-align: center;
}

/* Force display for problematic icons */
.fas::before, .far::before, .fab::before, .fa::before {
    display: inline-block !important;
    font-weight: inherit;
}

/* Specific fixes for common icons */
.header-icon i {
    font-size: 1.2em;
    line-height: 1;
}

.btn i {
    margin-right: 0.5em;
}

/* Fallback for when Font Awesome completely fails */
.no-fontawesome .fas::before,
.no-fontawesome .far::before,
.no-fontawesome .fab::before {
    font-family: inherit !important;
    font-weight: normal !important;
}

/* Alternative icon system using CSS shapes */
.css-icon {
    display: inline-block;
    width: 1em;
    height: 1em;
    position: relative;
    vertical-align: middle;
}

.css-icon-heart {
    background: currentColor;
    width: 13px;
    height: 11px;
    position: relative;
    transform: rotate(-45deg);
    margin: 0 5px;
}

.css-icon-heart::before,
.css-icon-heart::after {
    content: '';
    background: currentColor;
    border-radius: 50%;
    width: 7px;
    height: 7px;
    position: absolute;
}

.css-icon-heart::before {
    top: -3px;
    left: 0;
}

.css-icon-heart::after {
    top: 0;
    right: -3px;
}

.css-icon-search {
    border: 2px solid currentColor;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    position: relative;
}

.css-icon-search::after {
    content: '';
    position: absolute;
    bottom: -5px;
    right: -3px;
    width: 2px;
    height: 6px;
    background: currentColor;
    transform: rotate(45deg);
}

/* Media queries for better mobile support */
@media (max-width: 768px) {
    .fa-lg { font-size: 1.2em; }
    .fa-xl { font-size: 1.3em; }
    .fa-2x { font-size: 1.5em; }
    .fa-3x { font-size: 2em; }
    .fa-4x { font-size: 2.5em; }
    .fa-5x { font-size: 3em; }
}
